import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useGLTF } from '@react-three/drei';
import assetLoader from '../services/assetLoader';

// 3D Avatar component that loads actual GLB models
const Avatar3D = ({ gender = 'female', outfitTags }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);
  const [avatarPath, setAvatarPath] = useState(null);

  // Load avatar path from asset loader
  useEffect(() => {
    const loadAvatarPath = async () => {
      await assetLoader.loadManifest();
      const path = assetLoader.getAvatarPath(gender);
      console.log('🎭 Loading avatar path:', path, 'for gender:', gender);
      setAvatarPath(path);
    };
    loadAvatarPath();
  }, [gender]);

  // Process outfit tags when they change
  useEffect(() => {
    if (outfitTags) {
      assetLoader.processOutfitTags(outfitTags).then(setProcessedAssets);
    } else {
      setProcessedAssets(null);
    }
  }, [outfitTags]);

  // Gentle rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  // If we have an avatar path, try to load the GLB model
  if (avatarPath) {
    return <GLBAvatar avatarPath={avatarPath} outfitTags={outfitTags} gender={gender} />;
  }

  // Fallback to simple avatar
  return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
};

// GLB Avatar component that loads actual 3D models
const GLBAvatar = ({ avatarPath, outfitTags, gender }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);

  console.log('🎭 GLBAvatar attempting to load:', avatarPath);

  // Load the GLB model with error handling
  let scene, error;
  try {
    const gltf = useGLTF(avatarPath);
    scene = gltf.scene;

    // Log detailed information about the loaded model
    if (scene) {
      console.log('📐 GLB Scene details:', {
        children: scene.children.length,
        position: scene.position,
        scale: scene.scale,
        boundingBox: scene.userData
      });
    }
  } catch (loadError) {
    console.error('❌ GLB loading error:', loadError);
    error = loadError;
  }

  // Process outfit tags when they change
  useEffect(() => {
    if (outfitTags) {
      console.log('🎨 Processing outfit tags for GLB avatar:', outfitTags);
      assetLoader.processOutfitTags(outfitTags).then((processedAssets) => {
        console.log('🎭 Processed assets result:', processedAssets);
        setProcessedAssets(processedAssets);
      });
    } else {
      setProcessedAssets(null);
    }
  }, [outfitTags]);

  // Rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  if (error) {
    console.error('❌ Failed to load avatar GLB:', avatarPath, error);
    console.log('🔄 Falling back to SimpleAvatar');
    return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
  }

  if (!scene) {
    console.log('⏳ GLB scene not ready yet, showing SimpleAvatar');
    return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
  }

  console.log('✅ GLB Avatar loaded successfully:', scene);

  return (
    <group ref={groupRef} position={[0, -1, 0]} scale={[3, 3, 3]}>
      {/* Main avatar model */}
      <primitive object={scene.clone()} />

      {/* Render outfit components */}
      {processedAssets && <OutfitRenderer processedAssets={processedAssets} />}

      {/* Debug indicator to show GLB is loaded */}
      <mesh position={[0, 2, 0]}>
        <sphereGeometry args={[0.1]} />
        <meshStandardMaterial color="#00ff00" emissive="#00ff00" emissiveIntensity={0.5} />
      </mesh>
    </group>
  );
};

// Outfit Renderer component that loads and displays garments and accessories
const OutfitRenderer = ({ processedAssets }) => {
  console.log('👗 OutfitRenderer received processedAssets:', processedAssets);

  if (!processedAssets) {
    console.log('👗 No processed assets to render');
    return null;
  }

  console.log('👗 Rendering garments:', processedAssets.garments?.length || 0);
  console.log('👗 Rendering accessories:', processedAssets.accessories?.length || 0);

  return (
    <group>
      {/* Render garments */}
      {processedAssets.garments?.map((garment, index) => (
        <GarmentComponent key={`garment-${index}`} garment={garment} />
      ))}

      {/* Render accessories */}
      {processedAssets.accessories?.map((accessory, index) => (
        <AccessoryComponent key={`accessory-${index}`} accessory={accessory} />
      ))}

      {/* Debug indicators for outfit components */}
      {processedAssets.garments?.map((garment, index) => (
        <mesh key={`debug-garment-${index}`} position={[0.5 + index * 0.2, 1, 0]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial color="#ff0000" emissive="#ff0000" emissiveIntensity={0.3} />
        </mesh>
      ))}

      {processedAssets.accessories?.map((accessory, index) => (
        <mesh key={`debug-accessory-${index}`} position={[-0.5 - index * 0.2, 1, 0]}>
          <sphereGeometry args={[0.05]} />
          <meshStandardMaterial color="#0000ff" emissive="#0000ff" emissiveIntensity={0.3} />
        </mesh>
      ))}
    </group>
  );
};

// Individual garment component
const GarmentComponent = ({ garment }) => {
  console.log('👕 GarmentComponent rendering garment:', garment);

  if (!garment.path) {
    console.log('👕 No GLB path, using fallback geometry for:', garment.tag);
    // Fallback to colored geometry if no GLB path
    return (
      <mesh position={[0, 0, 0.1]}>
        <boxGeometry args={[0.8, 1.2, 0.3]} />
        <meshStandardMaterial color={garment.fallback_color || '#6B7280'} />
      </mesh>
    );
  }

  try {
    console.log('👕 Loading GLB for garment:', garment.path);
    const { scene } = useGLTF(garment.path);
    return scene ? <primitive object={scene.clone()} /> : null;
  } catch (error) {
    console.warn('👕 Failed to load garment:', garment.path, error);
    return (
      <mesh position={[0, 0, 0.1]}>
        <boxGeometry args={[0.8, 1.2, 0.3]} />
        <meshStandardMaterial color={garment.fallback_color || '#6B7280'} />
      </mesh>
    );
  }
};

// Individual accessory component
const AccessoryComponent = ({ accessory }) => {
  console.log('💍 AccessoryComponent rendering accessory:', accessory);

  if (!accessory.path) {
    console.log('💍 No GLB path, using fallback geometry for:', accessory.tag);
    // Fallback to colored geometry if no GLB path
    const position = accessory.position || [0, 0, 0];
    return (
      <mesh position={position}>
        <sphereGeometry args={[0.1, 8, 8]} />
        <meshStandardMaterial color={accessory.fallback_color || '#9CA3AF'} />
      </mesh>
    );
  }

  try {
    console.log('💍 Loading GLB for accessory:', accessory.path);
    const { scene } = useGLTF(accessory.path);
    const position = accessory.position || [0, 0, 0];
    return scene ? (
      <group position={position}>
        <primitive object={scene.clone()} />
      </group>
    ) : null;
  } catch (error) {
    console.warn('💍 Failed to load accessory:', accessory.path, error);
    const position = accessory.position || [0, 0, 0];
    return (
      <mesh position={position}>
        <sphereGeometry args={[0.1, 8, 8]} />
        <meshStandardMaterial color={accessory.fallback_color || '#9CA3AF'} />
      </mesh>
    );
  }
};

// Enhanced 3D Avatar with outfit visualization
const SimpleAvatar = ({ outfitTags, gender = 'female' }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);
  const [outfitColors, setOutfitColors] = useState({
    top: '#4F46E5',
    bottom: '#1F2937',
    shoes: '#111827'
  });

  useEffect(() => {
    if (outfitTags) {
      assetLoader.processOutfitTags(outfitTags).then(setProcessedAssets);

      // Extract colors from outfit tags for visualization
      const colors = extractOutfitColors(outfitTags);
      setOutfitColors(colors);
    } else {
      setProcessedAssets(null);
      setOutfitColors({
        top: '#4F46E5',
        bottom: '#1F2937',
        shoes: '#111827'
      });
    }
  }, [outfitTags]);

  // Gentle rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.15;
    }
  });

  // Determine body proportions based on gender
  const bodyScale = gender === 'male' ? [0.9, 2.1, 0.45] : [0.75, 2.0, 0.4];
  const shoulderWidth = gender === 'male' ? 0.7 : 0.6;

  return (
    <group ref={groupRef} position={[0, 0, 0]}>
      {/* Head */}
      <mesh position={[0, 1.4, 0]} castShadow>
        <sphereGeometry args={[0.32, 16, 16]} />
        <meshStandardMaterial color="#FBBF24" roughness={0.8} />
      </mesh>

      {/* Body/Torso - shows outfit top color */}
      <mesh position={[0, 0.2, 0]} castShadow>
        <boxGeometry args={bodyScale} />
        <meshStandardMaterial color={outfitColors.top} roughness={0.6} />
      </mesh>

      {/* Arms */}
      <mesh position={[-shoulderWidth, 0.4, 0]} castShadow>
        <capsuleGeometry args={[0.12, 1.0, 8, 16]} />
        <meshStandardMaterial color="#FBBF24" roughness={0.8} />
      </mesh>
      <mesh position={[shoulderWidth, 0.4, 0]} castShadow>
        <capsuleGeometry args={[0.12, 1.0, 8, 16]} />
        <meshStandardMaterial color="#FBBF24" roughness={0.8} />
      </mesh>

      {/* Legs/Bottom - shows outfit bottom color */}
      <mesh position={[-0.18, -1.4, 0]} castShadow>
        <capsuleGeometry args={[0.15, 1.2, 8, 16]} />
        <meshStandardMaterial color={outfitColors.bottom} roughness={0.6} />
      </mesh>
      <mesh position={[0.18, -1.4, 0]} castShadow>
        <capsuleGeometry args={[0.15, 1.2, 8, 16]} />
        <meshStandardMaterial color={outfitColors.bottom} roughness={0.6} />
      </mesh>

      {/* Feet/Shoes - shows shoe color */}
      <mesh position={[-0.18, -2.2, 0.1]} castShadow>
        <boxGeometry args={[0.25, 0.15, 0.4]} />
        <meshStandardMaterial color={outfitColors.shoes} roughness={0.4} />
      </mesh>
      <mesh position={[0.18, -2.2, 0.1]} castShadow>
        <boxGeometry args={[0.25, 0.15, 0.4]} />
        <meshStandardMaterial color={outfitColors.shoes} roughness={0.4} />
      </mesh>

      {/* Outfit accessories visualization */}
      {processedAssets && (
        <OutfitVisualization processedAssets={processedAssets} />
      )}

      {/* Simple outfit indicators when tags are present */}
      {outfitTags && (
        <group>
          {/* Hat/Accessory indicator */}
          {outfitTags.accessories?.includes('hat') && (
            <mesh position={[0, 1.8, 0]}>
              <cylinderGeometry args={[0.4, 0.35, 0.2, 8]} />
              <meshStandardMaterial color="#8B5CF6" roughness={0.5} />
            </mesh>
          )}

          {/* Belt indicator */}
          {outfitTags.accessories?.includes('belt') && (
            <mesh position={[0, -0.5, 0]}>
              <torusGeometry args={[0.45, 0.05, 8, 16]} />
              <meshStandardMaterial color="#92400E" roughness={0.3} />
            </mesh>
          )}
        </group>
      )}
    </group>
  );
};

// Helper function to extract colors from outfit tags
const extractOutfitColors = (outfitTags) => {
  const colors = {
    top: '#4F46E5',
    bottom: '#1F2937',
    shoes: '#111827'
  };

  if (!outfitTags) return colors;

  // Extract colors from various tag fields
  const allTags = [
    ...(outfitTags.garment_types || []),
    ...(outfitTags.colors || []),
    ...(outfitTags.materials || []),
    ...(outfitTags.style_descriptors || [])
  ].join(' ').toLowerCase();

  // Color mapping
  const colorMap = {
    red: '#EF4444',
    blue: '#3B82F6',
    green: '#10B981',
    yellow: '#F59E0B',
    purple: '#8B5CF6',
    pink: '#EC4899',
    orange: '#F97316',
    black: '#111827',
    white: '#F9FAFB',
    gray: '#6B7280',
    brown: '#92400E',
    gold: '#D97706',
    silver: '#9CA3AF'
  };

  // Find colors in tags
  Object.entries(colorMap).forEach(([colorName, colorValue]) => {
    if (allTags.includes(colorName)) {
      if (allTags.includes('top') || allTags.includes('shirt') || allTags.includes('jacket')) {
        colors.top = colorValue;
      } else if (allTags.includes('bottom') || allTags.includes('pants') || allTags.includes('skirt')) {
        colors.bottom = colorValue;
      } else if (allTags.includes('shoe') || allTags.includes('boot')) {
        colors.shoes = colorValue;
      } else {
        // Default to top if no specific garment mentioned
        colors.top = colorValue;
      }
    }
  });

  return colors;
};

// Component to visualize outfit based on processed assets
const OutfitVisualization = ({ processedAssets }) => {
  return (
    <group>
      {/* Garment overlays */}
      {processedAssets.garments?.map((garment, index) => (
        <GarmentOverlay
          key={index}
          garmentData={garment}
          position={[0, 0, 0.05 + index * 0.01]}
        />
      ))}

      {/* Accessories */}
      {processedAssets.accessories?.map((accessory, index) => (
        <AccessoryOverlay
          key={index}
          accessoryData={accessory}
          index={index}
        />
      ))}

      {/* Visual effects for vibes */}
      {processedAssets.vibes?.length > 0 && (
        <VibeEffects vibes={processedAssets.vibes} />
      )}
    </group>
  );
};

// Enhanced garment overlay visualization
const GarmentOverlay = ({ garmentData, position }) => {
  const { tag, fallback_color, category } = garmentData;

  // Adjust geometry based on garment category
  const getGarmentGeometry = (category) => {
    switch (category) {
      case 'bodysuit':
        return <boxGeometry args={[0.85, 2.1, 0.08]} />;
      case 'gown':
        return <boxGeometry args={[1.2, 2.5, 0.1]} />;
      case 'top':
        return <boxGeometry args={[0.9, 1.2, 0.1]} />;
      case 'bottom':
        return <boxGeometry args={[0.8, 1.0, 0.1]} />;
      case 'robe':
        return <boxGeometry args={[1.1, 2.3, 0.12]} />;
      case 'outerwear':
        return <boxGeometry args={[0.95, 1.4, 0.12]} />;
      default:
        return <boxGeometry args={[0.85, 2.1, 0.1]} />;
    }
  };

  // Adjust position based on category
  const getGarmentPosition = (category, basePosition) => {
    const [x, y, z] = basePosition;
    switch (category) {
      case 'top':
        return [x, y + 0.5, z];
      case 'bottom':
        return [x, y - 0.8, z];
      case 'outerwear':
        return [x, y + 0.3, z + 0.02];
      default:
        return basePosition;
    }
  };

  return (
    <mesh position={getGarmentPosition(category, position)}>
      {getGarmentGeometry(category)}
      <meshStandardMaterial
        color={fallback_color}
        transparent
        opacity={0.8}
        roughness={0.4}
        metalness={0.2}
      />
    </mesh>
  );
};

// Enhanced accessory visualization
const AccessoryOverlay = ({ accessoryData, index }) => {
  const { tag, fallback_color, position, attachment_point } = accessoryData;

  // Get accessory geometry based on type
  const getAccessoryGeometry = (tag) => {
    if (tag.includes('Halo') || tag.includes('Headpiece')) {
      return <torusGeometry args={[0.25, 0.05, 8, 16]} />;
    }
    if (tag.includes('Belt') || tag.includes('Obi')) {
      return <boxGeometry args={[0.9, 0.1, 0.05]} />;
    }
    if (tag.includes('Neckpiece') || tag.includes('Collar')) {
      return <torusGeometry args={[0.2, 0.03, 8, 16]} />;
    }
    if (tag.includes('Earrings')) {
      return <sphereGeometry args={[0.03]} />;
    }
    return <sphereGeometry args={[0.08]} />; // Default
  };

  // Handle special positioning for paired accessories
  const getAccessoryPositions = (tag, basePosition) => {
    if (tag.includes('Earrings')) {
      return [
        [-0.25, basePosition[1], basePosition[2]], // Left ear
        [0.25, basePosition[1], basePosition[2]]   // Right ear
      ];
    }
    if (tag.includes('Gauntlets')) {
      return [
        [-0.6, basePosition[1], basePosition[2]], // Left hand
        [0.6, basePosition[1], basePosition[2]]   // Right hand
      ];
    }
    return [basePosition]; // Single accessory
  };

  const positions = getAccessoryPositions(tag, position);

  return (
    <group>
      {positions.map((pos, idx) => (
        <mesh key={idx} position={pos}>
          {getAccessoryGeometry(tag)}
          <meshStandardMaterial
            color={fallback_color}
            metalness={0.7}
            roughness={0.3}
          />
        </mesh>
      ))}
    </group>
  );
};

// Vibe effects component for atmospheric enhancement
const VibeEffects = ({ vibes }) => {
  const effectsRef = useRef();

  useFrame((state) => {
    if (effectsRef.current) {
      // Subtle pulsing effect based on vibes
      const intensity = 0.5 + Math.sin(state.clock.elapsedTime * 2) * 0.2;
      effectsRef.current.children.forEach((child, index) => {
        if (child.material) {
          child.material.opacity = intensity * (0.1 + index * 0.05);
        }
      });
    }
  });

  // Create ambient effects based on vibe tags
  const getVibeEffects = () => {
    const effects = [];

    vibes.forEach((vibe, index) => {
      let color = '#8B5CF6'; // Default purple

      if (vibe.includes('Afrofuturist')) color = '#F59E0B';
      if (vibe.includes('TripHop')) color = '#3B82F6';
      if (vibe.includes('Digital')) color = '#06B6D4';
      if (vibe.includes('Ritual')) color = '#EC4899';

      effects.push(
        <mesh key={index} position={[0, 0, -0.5 - index * 0.1]}>
          <sphereGeometry args={[2 + index * 0.5, 16, 16]} />
          <meshStandardMaterial
            color={color}
            transparent
            opacity={0.05}
            side={2} // DoubleSide
          />
        </mesh>
      );
    });

    return effects;
  };

  return (
    <group ref={effectsRef}>
      {getVibeEffects()}
    </group>
  );
};

// Loading component
const LoadingSpinner = () => {
  const meshRef = useRef();

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.x += 0.02;
      meshRef.current.rotation.y += 0.02;
    }
  });

  return (
    <mesh ref={meshRef}>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshStandardMaterial color="#3B82F6" wireframe />
    </mesh>
  );
};

// Main ThreeScene component
const ThreeScene = ({ outfitTags, loading, gender = 'female' }) => {
  console.log('🎨 ThreeScene render:', { outfitTags, loading, gender });

  return (
    <div className="w-full h-full min-h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg overflow-hidden relative">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ background: 'transparent' }}
        onCreated={(state) => {
          console.log('✅ Three.js Canvas created successfully');
          console.log('📷 Camera position:', state.camera.position);
        }}
        fallback={
          <div className="flex items-center justify-center h-full text-gray-600">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading 3D Scene...</p>
            </div>
          </div>
        }
      >
        {/* Enhanced lighting for 3D models */}
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
        <directionalLight position={[-5, 5, 5]} intensity={0.8} />
        <pointLight position={[0, 5, 3]} intensity={0.5} color="#ffffff" />
        <spotLight position={[0, 10, 0]} intensity={0.3} angle={0.3} penumbra={1} />

        {/* Controls optimized for avatar viewing */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={6}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
          target={[0, 0, 0]}
        />

        {/* Scene content */}
        <Suspense fallback={<LoadingSpinner />}>
          {loading ? (
            <LoadingSpinner />
          ) : (
            <Avatar3D gender={gender} outfitTags={outfitTags} />
          )}
        </Suspense>

        {/* Subtle ground plane */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>
          <planeGeometry args={[10, 10]} />
          <meshStandardMaterial color="#E5E7EB" transparent opacity={0.3} />
        </mesh>
      </Canvas>
    </div>
  );
};

export default ThreeScene;