import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useGLTF } from '@react-three/drei';
import assetLoader from '../services/assetLoader';

// 3D Avatar component that loads actual GLB models
const Avatar3D = ({ gender = 'female', outfitTags }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);
  const [avatarPath, setAvatarPath] = useState(null);

  // Load avatar path from asset loader
  useEffect(() => {
    const loadAvatarPath = async () => {
      console.log('🔄 Loading manifest and avatar path for gender:', gender);
      await assetLoader.loadManifest();
      const path = assetLoader.getAvatarPath(gender);
      console.log('🎭 Avatar path loaded:', path, 'for gender:', gender);
      console.log('🔍 Setting avatar path state to:', path);

      // Add a temporary alert for debugging
      if (path) {
        console.log('✅ Avatar path found:', path);
      } else {
        console.log('❌ No avatar path found!');
      }

      setAvatarPath(path);
    };
    loadAvatarPath();
  }, [gender]);

  // Process outfit tags when they change
  useEffect(() => {
    if (outfitTags) {
      assetLoader.processOutfitTags(outfitTags).then(setProcessedAssets);
    } else {
      setProcessedAssets(null);
    }
  }, [outfitTags]);

  // Gentle rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  // If we have an avatar path, try to load the GLB model
  if (avatarPath) {
    console.log('✅ Avatar3D: Using GLBAvatar with path:', avatarPath);
    return <GLBAvatar avatarPath={avatarPath} outfitTags={outfitTags} gender={gender} />;
  }

  // Fallback to simple avatar
  console.log('⚠️ Avatar3D: No avatar path, using SimpleAvatar fallback');
  console.log('⚠️ Avatar3D: avatarPath value is:', avatarPath);
  return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
};

// GLB Avatar component that loads actual 3D models
const GLBAvatar = ({ avatarPath, outfitTags, gender }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);

  console.log('🎭 GLBAvatar attempting to load:', avatarPath);

  // Load the GLB model using useGLTF hook correctly - must be called before any conditional returns
  console.log('🔍 Attempting to load GLB from path:', avatarPath);
  const { scene, error } = useGLTF(avatarPath || '/assets/avatars/avatar_female_generic_mvp.glb');

  // Only try to render if we have a valid path
  if (!avatarPath) {
    console.log('❌ No avatar path provided, falling back to SimpleAvatar');
    return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
  }

  // Log detailed information about the loaded model
  useEffect(() => {
    if (scene) {
      console.log('📐 GLB Scene loaded successfully:', {
        children: scene.children.length,
        position: scene.position,
        scale: scene.scale,
        boundingBox: scene.userData
      });
    }
    if (error) {
      console.error('❌ GLB loading error:', error);
    }
  }, [scene, error]);

  // Process outfit tags when they change
  useEffect(() => {
    if (outfitTags) {
      console.log('🎨 Processing outfit tags for GLB avatar:', outfitTags);
      assetLoader.processOutfitTags(outfitTags).then((processedAssets) => {
        console.log('🎭 Processed assets result:', processedAssets);
        setProcessedAssets(processedAssets);
      });
    } else {
      setProcessedAssets(null);
    }
  }, [outfitTags]);

  // Rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1;
    }
  });

  if (error) {
    console.error('❌ Failed to load avatar GLB:', avatarPath, error);
    console.log('🔄 Falling back to SimpleAvatar');
    return <SimpleAvatar outfitTags={outfitTags} gender={gender} />;
  }

  if (!scene) {
    console.log('⏳ GLB scene not ready yet, showing green loading indicator');
    return (
      <group ref={groupRef} position={[0, -1.5, 0]} scale={[5, 5, 5]}>
        <mesh>
          <boxGeometry args={[1, 2, 0.5]} />
          <meshStandardMaterial color="#00FF00" />
        </mesh>
        <mesh position={[0, 1.2, 0]}>
          <sphereGeometry args={[0.3, 8, 8]} />
          <meshStandardMaterial color="#00FF00" />
        </mesh>
      </group>
    );
  }

  console.log('✅ GLB Avatar loaded successfully:', scene);

  return (
    <group ref={groupRef} position={[0, -1.5, 0]} scale={[5, 5, 5]}>
      {/* Main avatar model */}
      <primitive object={scene.clone()} />

      {/* Render outfit components */}
      {processedAssets && <OutfitRenderer processedAssets={processedAssets} />}
    </group>
  );
};

// Outfit Renderer component that loads and displays garments and accessories
const OutfitRenderer = ({ processedAssets }) => {
  console.log('👗 OutfitRenderer received processedAssets:', processedAssets);

  if (!processedAssets) {
    console.log('👗 No processed assets to render');
    return null;
  }

  console.log('👗 Rendering garments:', processedAssets.garments?.length || 0);
  console.log('👗 Rendering accessories:', processedAssets.accessories?.length || 0);

  return (
    <group>
      {/* Render garments */}
      {processedAssets.garments?.map((garment, index) => (
        <GarmentComponent key={`garment-${index}`} garment={garment} />
      ))}

      {/* Render accessories */}
      {processedAssets.accessories?.map((accessory, index) => (
        <AccessoryComponent key={`accessory-${index}`} accessory={accessory} />
      ))}


    </group>
  );
};

// Individual garment component
const GarmentComponent = ({ garment }) => {
  console.log('👕 GarmentComponent rendering garment:', garment);
  console.log('👕 Garment path:', garment.path);
  console.log('👕 Garment category:', garment.category);
  console.log('👕 Garment fallback_color:', garment.fallback_color);

  // Always call useGLTF at the top level, even if path is null
  const gltfPath = garment.path || '/assets/garments/oversized_hoodie.glb'; // fallback path
  const { scene, error } = useGLTF(gltfPath);

  if (!garment.path) {
    console.log('👕 No GLB path, using fallback geometry for:', garment.tag);
    // Fallback to colored geometry if no GLB path - make it larger and more visible
    const getGarmentGeometry = (category) => {
      switch (category) {
        case 'top':
          return <boxGeometry args={[1.2, 1.5, 0.2]} />;
        case 'bottom':
          return <boxGeometry args={[1.0, 1.8, 0.2]} />;
        case 'bodysuit':
          return <boxGeometry args={[1.1, 2.5, 0.15]} />;
        case 'gown':
          return <boxGeometry args={[1.4, 3.0, 0.15]} />;
        default:
          return <boxGeometry args={[1.0, 1.5, 0.2]} />;
      }
    };

    const getGarmentPosition = (category) => {
      switch (category) {
        case 'top':
          return [0, 0.5, 0.1];
        case 'bottom':
          return [0, -0.8, 0.1];
        case 'bodysuit':
        case 'gown':
          return [0, 0, 0.1];
        default:
          return [0, 0, 0.1];
      }
    };

    return (
      <mesh position={getGarmentPosition(garment.category)}>
        {getGarmentGeometry(garment.category)}
        <meshStandardMaterial
          color={garment.fallback_color || '#6B7280'}
          transparent
          opacity={0.8}
          roughness={0.4}
          metalness={0.2}
        />
      </mesh>
    );
  }

  // Handle GLB loading results
  if (error) {
    console.error('👕 Failed to load garment GLB:', garment.path, error);
    console.error('👕 Error details:', error.message || error);
      // Use the same improved fallback geometry
      const getGarmentGeometry = (category) => {
        switch (category) {
          case 'top':
            return <boxGeometry args={[1.2, 1.5, 0.2]} />;
          case 'bottom':
            return <boxGeometry args={[1.0, 1.8, 0.2]} />;
          case 'bodysuit':
            return <boxGeometry args={[1.1, 2.5, 0.15]} />;
          case 'gown':
            return <boxGeometry args={[1.4, 3.0, 0.15]} />;
          default:
            return <boxGeometry args={[1.0, 1.5, 0.2]} />;
        }
      };

      const getGarmentPosition = (category) => {
        switch (category) {
          case 'top':
            return [0, 0.5, 0.1];
          case 'bottom':
            return [0, -0.8, 0.1];
          case 'bodysuit':
          case 'gown':
            return [0, 0, 0.1];
          default:
            return [0, 0, 0.1];
        }
      };

      return (
        <mesh position={getGarmentPosition(garment.category)}>
          {getGarmentGeometry(garment.category)}
          <meshStandardMaterial
            color={garment.fallback_color || '#6B7280'}
            transparent
            opacity={0.8}
            roughness={0.4}
            metalness={0.2}
          />
        </mesh>
      );
    }

  if (!scene) {
    console.log('👕 Garment scene not ready yet for:', garment.path);
    return null;
  }

  console.log('👕 Garment GLB loaded successfully:', garment.path);
  return <primitive object={scene.clone()} />;
};

// Individual accessory component
const AccessoryComponent = ({ accessory }) => {
  console.log('💍 AccessoryComponent rendering accessory:', accessory);

  // Always call useGLTF at the top level, even if path is null
  const gltfPath = accessory.path || '/assets/accessories/belt_simple.glb'; // fallback path
  const { scene, error } = useGLTF(gltfPath);

  if (!accessory.path) {
    console.log('💍 No GLB path, using fallback geometry for:', accessory.tag);
    // Fallback to colored geometry if no GLB path
    const position = accessory.position || [0, 0, 0];
    return (
      <mesh position={position}>
        <sphereGeometry args={[0.1, 8, 8]} />
        <meshStandardMaterial color={accessory.fallback_color || '#9CA3AF'} />
      </mesh>
    );
  }

  // Handle GLB loading results
  const position = accessory.position || [0, 0, 0];

  if (error) {
    console.error('💍 Failed to load accessory GLB:', accessory.path, error);
    console.error('💍 Error details:', error.message || error);
    return (
      <mesh position={position}>
        <sphereGeometry args={[0.1, 8, 8]} />
        <meshStandardMaterial color={accessory.fallback_color || '#9CA3AF'} />
      </mesh>
    );
  }

  if (!scene) {
    console.log('💍 Accessory scene not ready yet for:', accessory.path);
    return null;
  }

  console.log('💍 Accessory GLB loaded successfully:', accessory.path);
  return (
    <group position={position}>
      <primitive object={scene.clone()} />
    </group>
  );
};

// Enhanced 3D Avatar with outfit visualization
const SimpleAvatar = ({ outfitTags, gender = 'female' }) => {
  const groupRef = useRef();
  const [processedAssets, setProcessedAssets] = useState(null);
  const [outfitColors, setOutfitColors] = useState({
    top: '#4F46E5',
    bottom: '#1F2937',
    shoes: '#111827'
  });

  useEffect(() => {
    if (outfitTags) {
      assetLoader.processOutfitTags(outfitTags).then(setProcessedAssets);

      // Extract colors from outfit tags for visualization
      const colors = extractOutfitColors(outfitTags);
      setOutfitColors(colors);
    } else {
      setProcessedAssets(null);
      setOutfitColors({
        top: '#4F46E5',
        bottom: '#1F2937',
        shoes: '#111827'
      });
    }
  }, [outfitTags]);

  // Gentle rotation animation
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.15;
    }
  });

  // Determine body proportions based on gender
  const bodyScale = gender === 'male' ? [0.9, 2.1, 0.45] : [0.75, 2.0, 0.4];
  const shoulderWidth = gender === 'male' ? 0.7 : 0.6;

  return (
    <group ref={groupRef} position={[0, -0.5, 0]} scale={[2, 2, 2]}>
      {/* Head */}
      <mesh position={[0, 1.4, 0]} castShadow>
        <sphereGeometry args={[0.32, 16, 16]} />
        <meshStandardMaterial color="#00FFFF" roughness={0.8} />
      </mesh>

      {/* Body/Torso - shows outfit top color */}
      <mesh position={[0, 0.2, 0]} castShadow>
        <boxGeometry args={bodyScale} />
        <meshStandardMaterial color="#00FFFF" roughness={0.6} />
      </mesh>

      {/* Arms */}
      <mesh position={[-shoulderWidth, 0.4, 0]} castShadow>
        <capsuleGeometry args={[0.12, 1.0, 8, 16]} />
        <meshStandardMaterial color="#FBBF24" roughness={0.8} />
      </mesh>
      <mesh position={[shoulderWidth, 0.4, 0]} castShadow>
        <capsuleGeometry args={[0.12, 1.0, 8, 16]} />
        <meshStandardMaterial color="#FBBF24" roughness={0.8} />
      </mesh>

      {/* Legs/Bottom - shows outfit bottom color */}
      <mesh position={[-0.18, -1.4, 0]} castShadow>
        <capsuleGeometry args={[0.15, 1.2, 8, 16]} />
        <meshStandardMaterial color={outfitColors.bottom} roughness={0.6} />
      </mesh>
      <mesh position={[0.18, -1.4, 0]} castShadow>
        <capsuleGeometry args={[0.15, 1.2, 8, 16]} />
        <meshStandardMaterial color={outfitColors.bottom} roughness={0.6} />
      </mesh>

      {/* Feet/Shoes - shows shoe color */}
      <mesh position={[-0.18, -2.2, 0.1]} castShadow>
        <boxGeometry args={[0.25, 0.15, 0.4]} />
        <meshStandardMaterial color={outfitColors.shoes} roughness={0.4} />
      </mesh>
      <mesh position={[0.18, -2.2, 0.1]} castShadow>
        <boxGeometry args={[0.25, 0.15, 0.4]} />
        <meshStandardMaterial color={outfitColors.shoes} roughness={0.4} />
      </mesh>

      {/* Render processed garments and accessories */}
      {processedAssets && (
        <group>
          {/* Render garments as overlays */}
          {processedAssets.garments?.map((garment, index) => (
            <GarmentOverlay
              key={`garment-${index}`}
              garmentData={garment}
              position={[0, 0, 0.1]}
            />
          ))}

          {/* Render accessories */}
          {processedAssets.accessories?.map((accessory, index) => (
            <AccessoryOverlay
              key={`accessory-${index}`}
              accessoryData={accessory}
              index={index}
            />
          ))}
        </group>
      )}

      {/* Simple outfit indicators when tags are present but no processed assets */}
      {outfitTags && !processedAssets && (
        <group>
          {/* Hat/Accessory indicator */}
          {outfitTags.accessories?.includes('hat') && (
            <mesh position={[0, 1.8, 0]}>
              <cylinderGeometry args={[0.4, 0.35, 0.2, 8]} />
              <meshStandardMaterial color="#8B5CF6" roughness={0.5} />
            </mesh>
          )}

          {/* Belt indicator */}
          {outfitTags.accessories?.includes('belt') && (
            <mesh position={[0, -0.5, 0]}>
              <torusGeometry args={[0.45, 0.05, 8, 16]} />
              <meshStandardMaterial color="#92400E" roughness={0.3} />
            </mesh>
          )}
        </group>
      )}
    </group>
  );
};

// Helper function to extract colors from outfit tags
const extractOutfitColors = (outfitTags) => {
  const colors = {
    top: '#4F46E5',
    bottom: '#1F2937',
    shoes: '#111827'
  };

  if (!outfitTags) return colors;

  // Extract colors from various tag fields
  const allTags = [
    ...(outfitTags.garment_types || []),
    ...(outfitTags.colors || []),
    ...(outfitTags.materials || []),
    ...(outfitTags.style_descriptors || [])
  ].join(' ').toLowerCase();

  // Color mapping
  const colorMap = {
    red: '#EF4444',
    blue: '#3B82F6',
    green: '#10B981',
    yellow: '#F59E0B',
    purple: '#8B5CF6',
    pink: '#EC4899',
    orange: '#F97316',
    black: '#111827',
    white: '#F9FAFB',
    gray: '#6B7280',
    brown: '#92400E',
    gold: '#D97706',
    silver: '#9CA3AF'
  };

  // Find colors in tags
  Object.entries(colorMap).forEach(([colorName, colorValue]) => {
    if (allTags.includes(colorName)) {
      if (allTags.includes('top') || allTags.includes('shirt') || allTags.includes('jacket')) {
        colors.top = colorValue;
      } else if (allTags.includes('bottom') || allTags.includes('pants') || allTags.includes('skirt')) {
        colors.bottom = colorValue;
      } else if (allTags.includes('shoe') || allTags.includes('boot')) {
        colors.shoes = colorValue;
      } else {
        // Default to top if no specific garment mentioned
        colors.top = colorValue;
      }
    }
  });

  return colors;
};

// Component to visualize outfit based on processed assets
const OutfitVisualization = ({ processedAssets }) => {
  return (
    <group>
      {/* Garment overlays */}
      {processedAssets.garments?.map((garment, index) => (
        <GarmentOverlay
          key={index}
          garmentData={garment}
          position={[0, 0, 0.05 + index * 0.01]}
        />
      ))}

      {/* Accessories */}
      {processedAssets.accessories?.map((accessory, index) => (
        <AccessoryOverlay
          key={index}
          accessoryData={accessory}
          index={index}
        />
      ))}

      {/* Visual effects for vibes */}
      {processedAssets.vibes?.length > 0 && (
        <VibeEffects vibes={processedAssets.vibes} />
      )}
    </group>
  );
};

// Accessory overlay visualization
const AccessoryOverlay = ({ accessoryData, index }) => {
  const { tag, fallback_color, attachment_point, position } = accessoryData;

  // Get position based on attachment point
  const getAccessoryPosition = (attachmentPoint, defaultPosition, index) => {
    if (defaultPosition) return defaultPosition;

    switch (attachmentPoint) {
      case 'waist':
        return [0, -0.2, 0.3];
      case 'ears':
        return index % 2 === 0 ? [-0.25, 1.4, 0.2] : [0.25, 1.4, 0.2];
      case 'head':
        return [0, 1.8, 0];
      case 'neck':
        return [0, 1.2, 0.2];
      case 'wrist':
        return index % 2 === 0 ? [-0.4, 0.2, 0.2] : [0.4, 0.2, 0.2];
      default:
        return [0, 0, 0.2];
    }
  };

  // Get geometry based on accessory type
  const getAccessoryGeometry = (tag) => {
    if (tag.includes('Belt')) {
      return <torusGeometry args={[0.45, 0.05, 8, 16]} />;
    }
    if (tag.includes('Earrings')) {
      return <sphereGeometry args={[0.08, 8, 8]} />;
    }
    if (tag.includes('Necklace')) {
      return <torusGeometry args={[0.2, 0.02, 8, 16]} />;
    }
    return <sphereGeometry args={[0.1, 8, 8]} />;
  };

  return (
    <mesh position={getAccessoryPosition(attachment_point, position, index)}>
      {getAccessoryGeometry(tag)}
      <meshStandardMaterial
        color={fallback_color || '#9CA3AF'}
        metalness={0.6}
        roughness={0.3}
      />
    </mesh>
  );
};

// Enhanced garment overlay visualization
const GarmentOverlay = ({ garmentData, position }) => {
  const { tag, fallback_color, category } = garmentData;

  // Adjust geometry based on garment category
  const getGarmentGeometry = (category) => {
    switch (category) {
      case 'bodysuit':
        return <boxGeometry args={[0.85, 2.1, 0.08]} />;
      case 'gown':
        return <boxGeometry args={[1.2, 2.5, 0.1]} />;
      case 'top':
        return <boxGeometry args={[0.9, 1.2, 0.1]} />;
      case 'bottom':
        return <boxGeometry args={[0.8, 1.0, 0.1]} />;
      case 'robe':
        return <boxGeometry args={[1.1, 2.3, 0.12]} />;
      case 'outerwear':
        return <boxGeometry args={[0.95, 1.4, 0.12]} />;
      default:
        return <boxGeometry args={[0.85, 2.1, 0.1]} />;
    }
  };

  // Adjust position based on category
  const getGarmentPosition = (category, basePosition) => {
    const [x, y, z] = basePosition;
    switch (category) {
      case 'top':
        return [x, y + 0.5, z];
      case 'bottom':
        return [x, y - 0.8, z];
      case 'outerwear':
        return [x, y + 0.3, z + 0.02];
      default:
        return basePosition;
    }
  };

  return (
    <mesh position={getGarmentPosition(category, position)}>
      {getGarmentGeometry(category)}
      <meshStandardMaterial
        color={fallback_color}
        transparent
        opacity={0.8}
        roughness={0.4}
        metalness={0.2}
      />
    </mesh>
  );
};



// Vibe effects component for atmospheric enhancement
const VibeEffects = ({ vibes }) => {
  const effectsRef = useRef();

  useFrame((state) => {
    if (effectsRef.current) {
      // Subtle pulsing effect based on vibes
      const intensity = 0.5 + Math.sin(state.clock.elapsedTime * 2) * 0.2;
      effectsRef.current.children.forEach((child, index) => {
        if (child.material) {
          child.material.opacity = intensity * (0.1 + index * 0.05);
        }
      });
    }
  });

  // Create ambient effects based on vibe tags
  const getVibeEffects = () => {
    const effects = [];

    vibes.forEach((vibe, index) => {
      let color = '#8B5CF6'; // Default purple

      if (vibe.includes('Afrofuturist')) color = '#F59E0B';
      if (vibe.includes('TripHop')) color = '#3B82F6';
      if (vibe.includes('Digital')) color = '#06B6D4';
      if (vibe.includes('Ritual')) color = '#EC4899';

      effects.push(
        <mesh key={index} position={[0, 0, -0.5 - index * 0.1]}>
          <sphereGeometry args={[2 + index * 0.5, 16, 16]} />
          <meshStandardMaterial
            color={color}
            transparent
            opacity={0.05}
            side={2} // DoubleSide
          />
        </mesh>
      );
    });

    return effects;
  };

  return (
    <group ref={effectsRef}>
      {getVibeEffects()}
    </group>
  );
};

// Loading component
const LoadingSpinner = () => {
  const meshRef = useRef();

  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.x += 0.02;
      meshRef.current.rotation.y += 0.02;
    }
  });

  return (
    <mesh ref={meshRef}>
      <boxGeometry args={[0.5, 0.5, 0.5]} />
      <meshStandardMaterial color="#3B82F6" wireframe />
    </mesh>
  );
};

// Main ThreeScene component
const ThreeScene = ({ outfitTags, loading, gender = 'female' }) => {
  console.log('🎨 ThreeScene render:', { outfitTags, loading, gender });

  return (
    <div className="w-full h-full min-h-[500px] bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg overflow-hidden relative">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ background: 'transparent' }}
        onCreated={(state) => {
          console.log('✅ Three.js Canvas created successfully');
          console.log('📷 Camera position:', state.camera.position);
        }}
        fallback={
          <div className="flex items-center justify-center h-full text-gray-600">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading 3D Scene...</p>
            </div>
          </div>
        }
      >
        {/* Enhanced lighting for 3D models */}
        <ambientLight intensity={0.6} />
        <directionalLight position={[10, 10, 5]} intensity={1.2} castShadow />
        <directionalLight position={[-5, 5, 5]} intensity={0.8} />
        <pointLight position={[0, 5, 3]} intensity={0.5} color="#ffffff" />
        <spotLight position={[0, 10, 0]} intensity={0.3} angle={0.3} penumbra={1} />

        {/* Controls optimized for avatar viewing */}
        <OrbitControls
          enablePan={false}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={6}
          minPolarAngle={Math.PI / 6}
          maxPolarAngle={Math.PI - Math.PI / 6}
          target={[0, 0, 0]}
        />

        {/* Scene content */}
        <Suspense fallback={<LoadingSpinner />}>
          {loading ? (
            <LoadingSpinner />
          ) : (
            <Avatar3D gender={gender} outfitTags={outfitTags} />
          )}
        </Suspense>

        {/* Subtle ground plane */}
        <mesh rotation={[-Math.PI / 2, 0, 0]} position={[0, -2, 0]} receiveShadow>
          <planeGeometry args={[10, 10]} />
          <meshStandardMaterial color="#E5E7EB" transparent opacity={0.3} />
        </mesh>
      </Canvas>
    </div>
  );
};

// Simple test component to verify GLB loading
const TestGLBLoader = () => {
  const testPath = '/assets/avatars/avatar_female_generic_mvp.glb';
  console.log('🧪 TestGLBLoader: Attempting to load:', testPath);

  try {
    const { scene, error } = useGLTF(testPath);

    if (error) {
      console.error('🧪 TestGLBLoader: Error loading GLB:', error);
      return (
        <mesh>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#FF0000" />
        </mesh>
      );
    }

    if (!scene) {
      console.log('🧪 TestGLBLoader: Scene not ready yet');
      return (
        <mesh>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#FFFF00" />
        </mesh>
      );
    }

    console.log('🧪 TestGLBLoader: Scene loaded successfully!', scene);
    return <primitive object={scene.clone()} scale={[5, 5, 5]} position={[0, -1.5, 0]} />;
  } catch (err) {
    console.error('🧪 TestGLBLoader: Exception:', err);
    return (
      <mesh>
        <boxGeometry args={[1, 1, 1]} />
        <meshStandardMaterial color="#FF00FF" />
      </mesh>
    );
  }
};

// Preload avatar models
useGLTF.preload('/assets/avatars/avatar_female_generic_mvp.glb');
useGLTF.preload('/assets/avatars/avatar_male_generic_mvp.glb');

export default ThreeScene;