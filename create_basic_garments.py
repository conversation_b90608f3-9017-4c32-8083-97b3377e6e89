#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create basic GLB garment files for the AI Stylist MVP.
Creates simple geometric shapes that can represent different garment types.
"""

import numpy as np
import trimesh
import os

def create_bodysuit():
    """Create a basic bodysuit shape"""
    # Create a cylinder for the torso
    torso = trimesh.creation.cylinder(radius=0.4, height=1.2)
    torso.apply_translation([0, 0.1, 0])

    # Create leg parts
    leg_left = trimesh.creation.cylinder(radius=0.15, height=0.8)
    leg_left.apply_translation([-0.2, -0.7, 0])

    leg_right = trimesh.creation.cylinder(radius=0.15, height=0.8)
    leg_right.apply_translation([0.2, -0.7, 0])

    # Create arm parts
    arm_left = trimesh.creation.cylinder(radius=0.1, height=0.6)
    rotation_matrix = trimesh.transformations.rotation_matrix(np.pi/2, [0, 0, 1])
    arm_left.apply_transform(rotation_matrix)
    arm_left.apply_translation([-0.5, 0.3, 0])

    arm_right = trimesh.creation.cylinder(radius=0.1, height=0.6)
    rotation_matrix = trimesh.transformations.rotation_matrix(-np.pi/2, [0, 0, 1])
    arm_right.apply_transform(rotation_matrix)
    arm_right.apply_translation([0.5, 0.3, 0])

    # Combine all parts
    bodysuit = trimesh.util.concatenate([torso, leg_left, leg_right, arm_left, arm_right])
    return bodysuit

def create_gown():
    """Create a basic floor-length gown shape"""
    # Create a cone for the flowing gown shape
    gown = trimesh.creation.cone(radius=0.6, height=1.8)
    gown.apply_translation([0, -0.3, 0])
    
    # Add a torso section
    torso = trimesh.creation.cylinder(radius=0.35, height=0.6)
    torso.apply_translation([0, 0.6, 0])
    
    # Combine
    gown = trimesh.util.concatenate([gown, torso])
    return gown

def create_hoodie():
    """Create a basic hoodie shape"""
    # Main body
    body = trimesh.creation.box(extents=[0.8, 0.6, 0.3])
    body.apply_translation([0, 0.2, 0])

    # Hood
    hood = trimesh.creation.icosphere(radius=0.25)
    hood.apply_translation([0, 0.6, 0.1])

    # Sleeves
    sleeve_left = trimesh.creation.cylinder(radius=0.12, height=0.5)
    rotation_matrix = trimesh.transformations.rotation_matrix(np.pi/2, [0, 0, 1])
    sleeve_left.apply_transform(rotation_matrix)
    sleeve_left.apply_translation([-0.5, 0.2, 0])

    sleeve_right = trimesh.creation.cylinder(radius=0.12, height=0.5)
    rotation_matrix = trimesh.transformations.rotation_matrix(-np.pi/2, [0, 0, 1])
    sleeve_right.apply_transform(rotation_matrix)
    sleeve_right.apply_translation([0.5, 0.2, 0])

    # Combine
    hoodie = trimesh.util.concatenate([body, hood, sleeve_left, sleeve_right])
    return hoodie

def create_wide_leg_pants():
    """Create wide leg pants"""
    # Left leg
    leg_left = trimesh.creation.cone(radius=0.25, height=1.0)
    leg_left.apply_translation([-0.2, -0.5, 0])
    
    # Right leg
    leg_right = trimesh.creation.cone(radius=0.25, height=1.0)
    leg_right.apply_translation([0.2, -0.5, 0])
    
    # Waistband
    waist = trimesh.creation.cylinder(radius=0.4, height=0.1)
    waist.apply_translation([0, 0.05, 0])
    
    # Combine
    pants = trimesh.util.concatenate([leg_left, leg_right, waist])
    return pants

def create_agbada():
    """Create a tech-enhanced Agbada (flowing traditional robe)"""
    # Main flowing body - wider at bottom
    body = trimesh.creation.cone(radius=0.8, height=1.6)
    body.apply_translation([0, -0.2, 0])

    # Wide sleeves
    sleeve_left = trimesh.creation.cone(radius=0.3, height=0.6)
    rotation_matrix = trimesh.transformations.rotation_matrix(np.pi/2, [0, 0, 1])
    sleeve_left.apply_transform(rotation_matrix)
    sleeve_left.apply_translation([-0.7, 0.3, 0])

    sleeve_right = trimesh.creation.cone(radius=0.3, height=0.6)
    rotation_matrix = trimesh.transformations.rotation_matrix(-np.pi/2, [0, 0, 1])
    sleeve_right.apply_transform(rotation_matrix)
    sleeve_right.apply_translation([0.7, 0.3, 0])

    # Combine
    agbada = trimesh.util.concatenate([body, sleeve_left, sleeve_right])
    return agbada

def create_belt():
    """Create a simple belt"""
    belt = trimesh.creation.cylinder(radius=0.45, height=0.05)
    belt.apply_translation([0, -0.1, 0])
    return belt

def main():
    """Create all garment GLB files"""
    # Create output directory
    output_dir = "frontend/public/assets/garments"
    os.makedirs(output_dir, exist_ok=True)
    
    # Create garments
    garments = {
        "bodysuit_basic.glb": create_bodysuit(),
        "gown_floor_length.glb": create_gown(),
        "hoodie_asymmetrical.glb": create_hoodie(),
        "pants_wide_leg.glb": create_wide_leg_pants(),
        "agbada_tech.glb": create_agbada(),
    }
    
    # Create accessories directory
    acc_dir = "frontend/public/assets/accessories"
    os.makedirs(acc_dir, exist_ok=True)
    
    accessories = {
        "belt_simple.glb": create_belt(),
    }
    
    # Export garments
    for filename, mesh in garments.items():
        filepath = os.path.join(output_dir, filename)
        mesh.export(filepath)
        print(f"Created: {filepath}")
    
    # Export accessories
    for filename, mesh in accessories.items():
        filepath = os.path.join(acc_dir, filename)
        mesh.export(filepath)
        print(f"Created: {filepath}")
    
    print("\nAll basic garment GLB files created successfully!")
    print("These are simple geometric representations that will be displayed on the avatar.")

if __name__ == "__main__":
    main()
